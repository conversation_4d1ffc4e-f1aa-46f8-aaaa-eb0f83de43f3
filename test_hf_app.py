#!/usr/bin/env python3
"""
Test script for the Hugging Face Gradio app
Validates functionality before deployment
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_imports():
    """Test that all required imports work."""
    print("🔍 Testing imports...")
    
    try:
        import gradio as gr
        print("✅ Gradio imported successfully")
    except ImportError as e:
        print(f"❌ Gradio import failed: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV imported successfully")
    except ImportError as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy imported successfully")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL imported successfully")
    except ImportError as e:
        print(f"❌ PIL import failed: {e}")
        return False
    
    try:
        import pytesseract
        print("✅ Pytesseract imported successfully")
    except ImportError as e:
        print(f"❌ Pytesseract import failed: {e}")
        return False
    
    try:
        from skimage.metrics import structural_similarity
        print("✅ Scikit-image imported successfully")
    except ImportError as e:
        print(f"❌ Scikit-image import failed: {e}")
        return False
    
    try:
        from reportlab.pdfgen import canvas
        print("✅ ReportLab imported successfully")
    except ImportError as e:
        print(f"❌ ReportLab import failed: {e}")
        return False
    
    return True

def test_system_dependencies():
    """Test system dependencies."""
    print("\n🔍 Testing system dependencies...")
    
    # Test yt-dlp
    try:
        import subprocess
        result = subprocess.run(['yt-dlp', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ yt-dlp available: {result.stdout.strip()}")
        else:
            print("❌ yt-dlp not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError) as e:
        print(f"❌ yt-dlp not found: {e}")
        return False
    
    # Test tesseract
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ Tesseract available: {version_line}")
        else:
            print("❌ Tesseract not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError) as e:
        print(f"❌ Tesseract not found: {e}")
        return False
    
    return True

def test_app_creation():
    """Test that the Gradio app can be created."""
    print("\n🔍 Testing app creation...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # Import the app
        from hf_app import create_interface
        
        # Create the interface
        app = create_interface()
        print("✅ Gradio interface created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        return False

def test_slide_extractor():
    """Test the SimpleSlideExtractor class."""
    print("\n🔍 Testing SlideExtractor...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from hf_app import SimpleSlideExtractor
        
        # Create a temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create extractor instance
            extractor = SimpleSlideExtractor(
                video_url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Test URL
                output_dir=temp_dir,
                callback=lambda msg: print(f"  📝 {msg}")
            )
            
            print("✅ SlideExtractor instance created successfully")
            
            # Test that methods exist
            assert hasattr(extractor, 'download_video')
            assert hasattr(extractor, 'extract_frames')
            assert hasattr(extractor, 'filter_slides')
            assert hasattr(extractor, 'extract_text')
            assert hasattr(extractor, 'create_pdf')
            assert hasattr(extractor, 'extract_slides')
            assert hasattr(extractor, 'get_slides')
            
            print("✅ All required methods present")
            
        return True
        
    except Exception as e:
        print(f"❌ SlideExtractor test failed: {e}")
        return False

def test_job_functions():
    """Test job management functions."""
    print("\n🔍 Testing job functions...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from hf_app import start_extraction, check_job_status
        
        # Test start_extraction with invalid URL
        result = start_extraction("")
        assert "Please enter a valid video URL" in result[0]
        print("✅ Input validation working")
        
        # Test check_job_status with invalid job ID
        result = check_job_status("")
        assert "No job ID provided" in result[0]
        print("✅ Job status checking working")
        
        return True
        
    except Exception as e:
        print(f"❌ Job functions test failed: {e}")
        return False

def run_all_tests():
    """Run all tests."""
    print("🧪 Running Hugging Face App Tests")
    print("=" * 40)
    
    tests = [
        ("Import Tests", test_imports),
        ("System Dependencies", test_system_dependencies),
        ("App Creation", test_app_creation),
        ("SlideExtractor", test_slide_extractor),
        ("Job Functions", test_job_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 20)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready for deployment.")
        return True
    else:
        print("⚠️  Some tests failed. Please fix issues before deployment.")
        return False

def main():
    """Main test function."""
    success = run_all_tests()
    
    if success:
        print("\n🚀 Next steps:")
        print("1. Run: python deploy_to_hf.py")
        print("2. Follow the deployment instructions")
        print("3. Deploy to Hugging Face Spaces")
        sys.exit(0)
    else:
        print("\n🔧 Fix the failing tests and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
