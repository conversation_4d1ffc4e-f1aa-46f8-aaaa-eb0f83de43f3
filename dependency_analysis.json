{"summary": {"total_unique_packages": 60, "configuration_counts": {"main": 31, "gradio": 18, "robust": 50}, "conflicts_found": 0, "security_issues": 0}, "configurations": {"main": {"opencv-python": "4.5.0", "numpy": "1.19.0", "pytesseract": "0.3.8", "Pillow": "8.0.0", "scikit-image": "0.18.0", "nltk": "3.6.0", "yt-dlp": "2023.1.6", "reportlab": "3.6.0", "matplotlib": "3.4.0", "flask": "2.0.0", "flask-cors": "4.0.0", "werkzeug": "2.0.0", "python-dotenv": "1.0.0", "moviepy": "1.0.0", "google-generativeai": "0.3.0", "gradio": "4.0.0", "gunicorn": "21.0.0", "psutil": "5.9.0", "requests": "2.31.0", "setuptools": "65.0.0", "redis": "4.5.0", "celery": "5.3.0", "sqlalchemy": "2.0.0", "alembic": "1.12.0", "psycopg2-binary": "2.9.0", "flask-sqlalchemy": "3.0.0", "flask-migrate": "4.0.0", "selenium": "4.15.0", "pytube": "15.0.0", "webdriver-manager": "4.0.0", "youtube-transcript-api": "0.6.0"}, "gradio": {"gradio": "4.0.0", "opencv-python": "4.5.0", "numpy": "1.19.0", "Pillow": "8.0.0", "scikit-image": "0.18.0", "pytesseract": "0.3.8", "nltk": "3.6.0", "yt-dlp": "2023.1.6", "moviepy": "1.0.0", "reportlab": "3.6.0", "matplotlib": "3.4.0", "requests": "2.31.0", "google-generativeai": "0.3.0", "psutil": "5.9.0", "setuptools": "65.0.0", "selenium": "4.15.0", "pytube": "15.0.0", "webdriver-manager": "4.0.0"}, "robust": {"gradio": "4.0.0", "opencv-python": "4.5.0", "numpy": "1.19.0", "Pillow": "8.0.0", "scikit-image": "0.18.0", "pytesseract": "0.3.8", "nltk": "3.6.0", "yt-dlp": "2023.1.6", "pytube": "15.0.0", "youtube-dl": "2021.12.17", "youtube-transcript-api": "0.6.0", "moviepy": "1.0.0", "ffmpeg-python": "0.2.0", "reportlab": "3.6.0", "matplotlib": "3.4.0", "requests": "2.31.0", "httpx": "0.24.0", "aiohttp": "3.9.0", "google-generativeai": "0.3.0", "psutil": "5.9.0", "selenium": "4.15.0", "webdriver-manager": "4.0.0", "fake-useragent": "1.4.0", "pysocks": "1.7.1", "asyncio": "3.4.3", "aiofiles": "23.0.0", "pandas": "1.3.0", "json5": "0.9.0", "structlog": "23.0.0", "rich": "13.0.0", "setuptools": "65.0.0", "certifi": "2023.0.0", "urllib3": "1.26.0", "cryptography": "41.0.0", "pafy": "0.5.5", "youtube-search-python": "1.6.6", "textblob": "0.17.1", "spacy": "3.4.0", "opencv-contrib-python": "4.5.0", "imageio": "2.22.0", "librosa": "0.9.0", "soundfile": "0.12.0", "sqlalchemy": "2.0.0", "sqlite3  # Built-in with Python": "any", "python-dotenv": "1.0.0", "pyyaml": "6.0.0", "tenacity": "8.2.0", "backoff": "2.2.0", "memory-profiler": "0.61.0", "line-profiler": "4.0.0"}}, "conflicts": [], "security_issues": [], "categorized_packages": {"other": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "pytesseract", "werkzeug", "aiohttp", "requests", "tenacity", "sqlite3  # Built-in with Python", "selenium", "pysocks", "certifi", "textblob", "asyncio", "httpx", "Pillow", "reportlab", "spacy", "urllib3", "pyyaml", "celery", "soundfile", "line-profiler", "structlog", "flask-cors", "youtube-transcript-api", "psutil", "json5", "python-dotenv", "gunicorn", "youtube-search-python", "cryptography", "aiofiles", "rich", "youtube-dl", "setuptools", "librosa", "fake-useragent", "webdriver-manager", "pafy", "flask-migrate", "backoff", "flask-sqlalchemy", "nltk", "memory-profiler"], "ml_ai": ["google-generativeai"], "video_processing": ["ffmpeg-python", "yt-dlp", "moviepy", "pytube"], "computer_vision": ["scikit-image", "imageio", "opencv-contrib-python", "opencv-python"], "database": ["alembic", "sqlalchemy", "psycopg2-binary", "redis"], "data_processing": ["pandas", "numpy"], "web_framework": ["flask", "gradio"]}, "import_usage": {"os": ["advanced_youtube_downloader.py", "app.py", "celery_config.py", "celery_worker.py", "content_analyzer.py", "cors_config.py", "dependency_analyzer.py", "deployment_bot_detection_fix.py", "deployment_environment_fix.py", "deployment_environment_fix.py", "deployment_hotfix.py", "enhanced_slide_extractor.py", "enhanced_youtube_downloader.py", "fix_dependency_conflicts.py", "fix_deployment.py", "gemini_transcription.py", "gradio_full_app.py", "gradio_interface.py", "hotfix_job_id_compatibility.py", "integrate_enhanced_downloader.py", "integrate_enhanced_downloader.py", "job_storage.py", "ocr_context_enhancer.py", "quick_test.py", "robust_slide_extractor.py", "robust_youtube_downloader.py", "simple_integration.py", "simple_integration.py", "slide_description_generator.py", "slide_extractor.py", "start_app.py", "start_gradio.py", "start_gradio_app.py", "syllabus_manager.py", "tasks.py", "test_deployment_environment.py", "test_integration.py", "test_youtube_download.py", "windows_integration.py", "windows_integration.py", "youtube_download_fix.py"], "subprocess": ["advanced_youtube_downloader.py", "dependency_analyzer.py", "deployment_bot_detection_fix.py", "deployment_environment_fix.py", "deployment_environment_fix.py", "deployment_hotfix.py", "enhanced_youtube_downloader.py", "fix_deployment.py", "gemini_transcription.py", "quick_test.py", "robust_youtube_downloader.py", "slide_extractor.py", "start_gradio_app.py", "test_deployment_environment.py", "test_youtube_download.py", "windows_integration.py", "youtube_download_fix.py"], "tempfile": ["advanced_youtube_downloader.py", "app.py", "deployment_environment_fix.py", "enhanced_youtube_downloader.py", "gemini_transcription.py", "gradio_full_app.py", "quick_test.py", "robust_slide_extractor.py", "robust_youtube_downloader.py", "test_deployment_environment.py", "test_integration.py", "test_youtube_download.py", "windows_integration.py", "youtube_download_fix.py"], "logging": ["advanced_youtube_downloader.py", "app.py", "celery_worker.py", "content_analyzer.py", "dependency_analyzer.py", "deployment_bot_detection_fix.py", "deployment_environment_fix.py", "deployment_hotfix.py", "enhanced_slide_extractor.py", "enhanced_youtube_downloader.py", "fix_dependency_conflicts.py", "fix_deployment.py", "gemini_transcription.py", "gradio_full_app.py", "hotfix_job_id_compatibility.py", "integrate_enhanced_downloader.py", "job_storage.py", "ocr_context_enhancer.py", "robust_slide_extractor.py", "robust_youtube_downloader.py", "simple_integration.py", "slide_description_generator.py", "slide_extractor.py", "start_app.py", "syllabus_manager.py", "tasks.py", "test_youtube_download.py", "windows_integration.py", "youtube_download_fix.py"], "time": ["advanced_youtube_downloader.py", "app.py", "deployment_hotfix.py", "enhanced_youtube_downloader.py", "gemini_transcription.py", "gradio_full_app.py", "gradio_interface.py", "robust_youtube_downloader.py", "slide_extractor.py", "start_app.py", "start_gradio.py", "tasks.py", "youtube_download_fix.py"], "random": ["advanced_youtube_downloader.py", "enhanced_youtube_downloader.py", "robust_youtube_downloader.py", "slide_extractor.py", "youtube_download_fix.py"], "json": ["advanced_youtube_downloader.py", "app.py", "content_analyzer.py", "dependency_analyzer.py", "enhanced_slide_extractor.py", "enhanced_youtube_downloader.py", "gemini_transcription.py", "gradio_full_app.py", "gradio_interface.py", "job_storage.py", "models.py", "ocr_context_enhancer.py", "robust_youtube_downloader.py", "slide_description_generator.py", "slide_extractor.py", "start_gradio.py", "syllabus_manager.py"], "typing": ["advanced_youtube_downloader.py", "dependency_analyzer.py", "enhanced_youtube_downloader.py", "gradio_full_app.py", "gradio_interface.py", "job_storage.py", "models.py", "robust_slide_extractor.py", "robust_youtube_downloader.py", "start_gradio.py", "tasks.py"], "sys": ["app.py", "celery_worker.py", "dependency_analyzer.py", "deployment_bot_detection_fix.py", "deployment_environment_fix.py", "deployment_environment_fix.py", "deployment_hotfix.py", "fix_deployment.py", "gemini_transcription.py", "gradio_full_app.py", "hotfix_job_id_compatibility.py", "integrate_enhanced_downloader.py", "integrate_enhanced_downloader.py", "robust_slide_extractor.py", "robust_youtube_downloader.py", "simple_integration.py", "simple_integration.py", "slide_description_generator.py", "start_app.py", "start_gradio_app.py", "test_deployment_environment.py", "test_integration.py", "test_youtube_download.py", "windows_integration.py"], "flask": ["app.py"], "flask_cors": ["app.py", "cors_config.py"], "flask_sqlalchemy": ["app.py", "models.py"], "flask_migrate": ["app.py"], "werkzeug": ["app.py"], "threading": ["app.py", "gradio_full_app.py", "gradio_interface.py", "slide_extractor.py"], "datetime": ["app.py", "deployment_hotfix.py", "fix_dependency_conflicts.py", "gradio_full_app.py", "gradio_interface.py", "integrate_enhanced_downloader.py", "job_storage.py", "models.py", "robust_youtube_downloader.py", "simple_integration.py", "slide_extractor.py", "tasks.py", "windows_integration.py"], "slide_extractor": ["app.py", "enhanced_slide_extractor.py"], "enhanced_slide_extractor": ["app.py", "tasks.py"], "models": ["app.py", "job_storage.py", "tasks.py"], "job_storage": ["app.py", "tasks.py"], "celery_config": ["app.py", "celery_worker.py", "tasks.py"], "celery": ["celery_config.py", "celery_worker.py", "celery_worker.py", "tasks.py", "tasks.py"], "kombu": ["celery_config.py"], "re": ["content_analyzer.py", "dependency_analyzer.py", "gemini_transcription.py", "ocr_context_enhancer.py", "slide_description_generator.py", "slide_extractor.py"], "nltk": ["content_analyzer.py", "content_analyzer.py", "content_analyzer.py", "content_analyzer.py"], "collections": ["content_analyzer.py", "dependency_analyzer.py", "enhanced_slide_extractor.py", "slide_extractor.py", "syllabus_manager.py"], "string": ["content_analyzer.py"], "math": ["content_analyzer.py"], "numpy": ["content_analyzer.py", "ocr_context_enhancer.py", "slide_extractor.py"], "pathlib": ["dependency_analyzer.py", "fix_dependency_conflicts.py", "gradio_full_app.py", "robust_slide_extractor.py", "robust_youtube_downloader.py", "slide_extractor.py"], "platform": ["deployment_environment_fix.py", "deployment_environment_fix.py", "test_deployment_environment.py"], "content_analyzer": ["enhanced_slide_extractor.py", "tasks.py"], "syllabus_manager": ["enhanced_slide_extractor.py"], "shutil": ["fix_dependency_conflicts.py", "gradio_full_app.py", "integrate_enhanced_downloader.py", "simple_integration.py", "slide_extractor.py", "test_integration.py", "windows_integration.py"], "gradio": ["gradio_full_app.py", "gradio_interface.py", "start_gradio.py"], "uuid": ["gradio_full_app.py"], "sqlite3": ["gradio_full_app.py"], "requests": ["gradio_interface.py", "robust_youtube_downloader.py", "slide_extractor.py", "start_gradio.py"], "redis": ["job_storage.py"], "sqlalchemy": ["models.py", "models.py"], "cv2": ["ocr_context_enhancer.py", "slide_extractor.py"], "pytesseract": ["ocr_context_enhancer.py", "slide_extractor.py"], "PIL": ["ocr_context_enhancer.py", "slide_extractor.py"], "hashlib": ["robust_youtube_downloader.py"], "importlib": ["slide_description_generator.py", "start_gradio_app.py"], "io": ["slide_extractor.py"], "argparse": ["slide_extractor.py"], "multiprocessing": ["slide_extractor.py"], "urllib": ["slide_extractor.py"], "concurrent": ["slide_extractor.py"], "skimage": ["slide_extractor.py"], "functools": ["slide_extractor.py"], "traceback": ["tasks.py"], "gemini_transcription": ["tasks.py"], "slide_description_generator": ["tasks.py"]}, "recommendations": ["🧹 Review 50 potentially unused packages", "✅ No known security issues found", "📌 Pin exact versions for production deployment", "🐳 Use multi-stage Docker builds to reduce image size", "🔄 Implement automated dependency updates", "📊 Regular dependency audits and cleanup"]}