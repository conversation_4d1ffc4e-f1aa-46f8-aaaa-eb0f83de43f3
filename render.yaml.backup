services:
  # PostgreSQL Database
  - type: pserv
    name: slide-extractor-db
    env: docker
    plan: starter
    dockerfilePath: ./Dockerfile.postgres
    envVars:
      - key: POSTGRES_DB
        value: slide_extractor
      - key: POSTGRES_USER
        value: slide_user
      - key: POSTGRES_PASSWORD
        generateValue: true

  # Redis Cache and Message Broker
  - type: redis
    name: slide-extractor-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru

  # Main API Service
  - type: web
    name: slide-extractor-api
    env: python
    buildCommand: |
      pip install --upgrade pip &&
      pip install -r requirements.txt &&
      flask db upgrade
    startCommand: gunicorn app:app --bind 0.0.0.0:$PORT --workers 3 --timeout 600 --max-requests 1000 --max-requests-jitter 100
    plan: standard
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PORT
        value: 10000
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: slide-extractor-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: slide-extractor-redis
          property: connectionString
      - key: USE_CELERY
        value: "true"
      - key: CORS_ALLOWED_ORIGINS
        value: "https://latenighter.netlify.app"
      - key: GEMINI_API_KEY
        sync: false  # Set manually in dashboard
      - key: MAX_CONTENT_LENGTH
        value: "104857600"  # 100MB
      - key: UPLOAD_TIMEOUT
        value: "600"  # 10 minutes
      - key: FLASK_APP
        value: app.py
    healthCheckPath: /api/health

  # Celery Worker for General Tasks
  - type: worker
    name: slide-extractor-worker-general
    env: python
    buildCommand: pip install --upgrade pip && pip install -r requirements.txt
    startCommand: python celery_worker.py worker --worker-type general --concurrency 2
    plan: standard
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: slide-extractor-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: slide-extractor-redis
          property: connectionString
      - key: GEMINI_API_KEY
        sync: false
    scaling:
      minInstances: 1
      maxInstances: 3

  # Celery Worker for Extraction Tasks (CPU Intensive)
  - type: worker
    name: slide-extractor-worker-extraction
    env: python
    buildCommand: pip install --upgrade pip && pip install -r requirements.txt
    startCommand: python celery_worker.py worker --worker-type extraction --concurrency 1
    plan: standard_plus  # More CPU/memory for video processing
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: slide-extractor-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: slide-extractor-redis
          property: connectionString
      - key: GEMINI_API_KEY
        sync: false
    scaling:
      minInstances: 1
      maxInstances: 5

  # Celery Worker for Analysis Tasks (AI/ML)
  - type: worker
    name: slide-extractor-worker-analysis
    env: python
    buildCommand: pip install --upgrade pip && pip install -r requirements.txt
    startCommand: python celery_worker.py worker --worker-type analysis --concurrency 2
    plan: standard
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: slide-extractor-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: slide-extractor-redis
          property: connectionString
      - key: GEMINI_API_KEY
        sync: false
    scaling:
      minInstances: 1
      maxInstances: 3

  # Celery Beat Scheduler
  - type: worker
    name: slide-extractor-beat
    env: python
    buildCommand: pip install --upgrade pip && pip install -r requirements.txt
    startCommand: python celery_worker.py beat
    plan: starter
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: slide-extractor-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: slide-extractor-redis
          property: connectionString
    scaling:
      minInstances: 1
      maxInstances: 1  # Only one beat scheduler needed

  # Flower Monitoring (Optional)
  - type: web
    name: slide-extractor-flower
    env: python
    buildCommand: pip install --upgrade pip && pip install -r requirements.txt && pip install flower
    startCommand: python celery_worker.py flower
    plan: starter
    envVars:
      - key: REDIS_URL
        fromService:
          type: redis
          name: slide-extractor-redis
          property: connectionString
    # Optional service - can be disabled in production
    suspend: true
