#!/usr/bin/env python3
"""
Local testing script for the Hugging Face app
Run this to test the app locally before deploying
"""

import os
import sys
import subprocess

def check_dependencies():
    """Check if all dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    try:
        import gradio
        print(f"✅ Gradio {gradio.__version__}")
    except ImportError:
        print("❌ Gradio not installed. Run: pip install gradio")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV not installed. Run: pip install opencv-python-headless")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__}")
    except ImportError:
        print("❌ NumPy not installed. Run: pip install numpy")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow")
    except ImportError:
        print("❌ Pillow not installed. Run: pip install Pillow")
        return False
    
    try:
        import pytesseract
        print("✅ Pytesseract")
    except ImportError:
        print("❌ Pytesseract not installed. Run: pip install pytesseract")
        return False
    
    try:
        from skimage.metrics import structural_similarity
        print("✅ Scikit-image")
    except ImportError:
        print("❌ Scikit-image not installed. Run: pip install scikit-image")
        return False
    
    try:
        from reportlab.pdfgen import canvas
        print("✅ ReportLab")
    except ImportError:
        print("❌ ReportLab not installed. Run: pip install reportlab")
        return False
    
    return True

def check_system_tools():
    """Check system tools."""
    print("\n🔍 Checking system tools...")
    
    # Check yt-dlp
    try:
        result = subprocess.run(['yt-dlp', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ yt-dlp {result.stdout.strip()}")
        else:
            print("❌ yt-dlp not working")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ yt-dlp not found. Install with: pip install yt-dlp")
        return False
    
    # Check tesseract
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version = result.stdout.split('\n')[0]
            print(f"✅ {version}")
        else:
            print("❌ Tesseract not working")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Tesseract not found. Install tesseract-ocr on your system")
        return False
    
    return True

def install_requirements():
    """Install requirements from requirements.txt."""
    print("\n📦 Installing requirements...")
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], check=True, capture_output=True, text=True)
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        print(f"Error output: {e.stderr}")
        return False

def run_app():
    """Run the Gradio app."""
    print("\n🚀 Starting Gradio app...")
    print("The app will open in your browser at http://localhost:7860")
    print("Press Ctrl+C to stop the app")
    
    try:
        # Import and run the app
        from app import create_interface
        
        app = create_interface()
        app.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            debug=True
        )
    except KeyboardInterrupt:
        print("\n👋 App stopped by user")
    except Exception as e:
        print(f"❌ Error running app: {e}")

def main():
    """Main function."""
    print("🎓 YouTube Slide Extractor - Local Testing")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("app.py"):
        print("❌ app.py not found. Make sure you're in the hf_deployment directory.")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        print("\n💡 Install missing dependencies and try again.")
        print("You can install all at once with: pip install -r requirements.txt")
        
        install_choice = input("\nWould you like to install requirements now? (y/n): ")
        if install_choice.lower() == 'y':
            if install_requirements():
                print("✅ Requirements installed. Please restart this script.")
            sys.exit(0)
        else:
            sys.exit(1)
    
    # Check system tools
    if not check_system_tools():
        print("\n💡 Install missing system tools and try again.")
        print("For yt-dlp: pip install yt-dlp")
        print("For tesseract: install tesseract-ocr on your system")
        sys.exit(1)
    
    print("\n✅ All dependencies satisfied!")
    
    # Run the app
    run_app()

if __name__ == "__main__":
    main()
