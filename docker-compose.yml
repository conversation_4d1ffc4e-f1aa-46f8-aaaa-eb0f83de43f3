# Docker Compose configuration for scalable Slide Extractor deployment

version: '3.8'

services:
  # Redis for caching and message broker
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: slide_extractor
      POSTGRES_USER: slide_user
      POSTGRES_PASSWORD: slide_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U slide_user -d slide_extractor"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Main API application
  api:
    build:
      context: .
      target: production
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=****************************************************/slide_extractor
      - REDIS_URL=redis://redis:6379/0
      - USE_CELERY=true
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:3000}
    volumes:
      - slides_data:/app/slides
      - logs_data:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery worker for general tasks
  worker-general:
    build:
      context: .
      target: worker
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=****************************************************/slide_extractor
      - REDIS_URL=redis://redis:6379/0
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - slides_data:/app/slides
      - logs_data:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["python", "celery_worker.py", "worker", "--worker-type", "general", "--concurrency", "2"]
    deploy:
      replicas: 2

  # Celery worker for extraction tasks (CPU intensive)
  worker-extraction:
    build:
      context: .
      target: worker
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=****************************************************/slide_extractor
      - REDIS_URL=redis://redis:6379/0
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - slides_data:/app/slides
      - logs_data:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["python", "celery_worker.py", "worker", "--worker-type", "extraction", "--concurrency", "1"]
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  # Celery worker for analysis tasks (AI/ML)
  worker-analysis:
    build:
      context: .
      target: worker
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=****************************************************/slide_extractor
      - REDIS_URL=redis://redis:6379/0
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - slides_data:/app/slides
      - logs_data:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["python", "celery_worker.py", "worker", "--worker-type", "analysis", "--concurrency", "2"]
    deploy:
      replicas: 2

  # Celery beat scheduler
  beat:
    build:
      context: .
      target: beat
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=****************************************************/slide_extractor
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - beat_data:/tmp
      - logs_data:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Flower monitoring (optional)
  flower:
    build:
      context: .
      target: flower
    restart: unless-stopped
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    profiles:
      - monitoring

  # Nginx load balancer (for multiple API instances)
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    profiles:
      - loadbalancer

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  slides_data:
    driver: local
  logs_data:
    driver: local
  beat_data:
    driver: local

networks:
  default:
    driver: bridge
