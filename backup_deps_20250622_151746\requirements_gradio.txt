# Gradio Full Stack Slide Extractor Requirements
# Core dependencies for the Gradio application

# Web Interface
gradio>=4.0.0

# Computer Vision and Image Processing
opencv-python>=4.5.0
numpy>=1.19.0
Pillow>=8.0.0
scikit-image>=0.18.0

# OCR and Text Processing
pytesseract>=0.3.8
nltk>=3.6.0

# Video Processing
yt-dlp>=2022.1.21
moviepy>=1.0.0

# PDF Generation
reportlab>=3.6.0

# Plotting and Visualization
matplotlib>=3.4.0

# HTTP Requests
requests>=2.31.0

# AI and Machine Learning (Optional)
google-generativeai>=0.3.0

# System Utilities
psutil>=5.9.0

# Development and Debugging
setuptools>=65.0.0

# Enhanced YouTube Download (Optional)
selenium>=4.15.0
pytube>=15.0.0
webdriver-manager>=4.0.0

# Note: Some dependencies are optional and the application will work
# with reduced functionality if they are not installed.
# 
# For basic functionality, install:
# pip install gradio opencv-python numpy Pillow requests
#
# For full functionality, install all dependencies:
# pip install -r requirements_gradio.txt
