# 🚀 Hugging Face Spaces Deployment Guide

This guide will help you deploy the YouTube Slide Extractor to Hugging Face Spaces.

## 📋 Prerequisites

1. **Hugging Face Account**: Create an account at [huggingface.co](https://huggingface.co)
2. **Git**: Ensure Git is installed on your system
3. **Git LFS**: Install Git Large File Storage for handling large files

## 🔧 Deployment Steps

### Step 1: Create a New Space

1. Go to [Hugging Face Spaces](https://huggingface.co/spaces)
2. Click "Create new Space"
3. Fill in the details:
   - **Space name**: `youtube-slide-extractor` (or your preferred name)
   - **License**: MIT
   - **SDK**: Gradio
   - **Hardware**: CPU Basic (free tier) or upgrade for better performance
   - **Visibility**: Public or Private (your choice)

### Step 2: Clone the Repository

```bash
# Clone your new space repository
git clone https://huggingface.co/spaces/YOUR_USERNAME/youtube-slide-extractor
cd youtube-slide-extractor

# Initialize Git LFS
git lfs install
```

### Step 3: Copy Required Files

Copy these files from your project to the Hugging Face Space directory:

**Required Files:**
```
hf_app.py → app.py
requirements_hf.txt → requirements.txt
packages.txt
README_HF.md → README.md
```

**Copy Commands:**
```bash
# Copy the main application file
cp /path/to/your/project/hf_app.py ./app.py

# Copy requirements
cp /path/to/your/project/requirements_hf.txt ./requirements.txt

# Copy system packages
cp /path/to/your/project/packages.txt ./packages.txt

# Copy README
cp /path/to/your/project/README_HF.md ./README.md
```

### Step 4: Update README Header

Edit the `README.md` file to update the header with your space details:

```yaml
---
title: YouTube Slide Extractor
emoji: 🎓
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.0.0
app_file: app.py
pinned: false
license: mit
---
```

### Step 5: Commit and Push

```bash
# Add all files
git add .

# Commit changes
git commit -m "Initial deployment of YouTube Slide Extractor"

# Push to Hugging Face
git push origin main
```

### Step 6: Monitor Deployment

1. Go to your Space page on Hugging Face
2. Watch the build logs in the "Logs" tab
3. Wait for the deployment to complete (usually 5-10 minutes)
4. Test the application once it's running

## 🔧 Configuration Options

### Hardware Upgrades

For better performance, consider upgrading the hardware:

- **CPU Basic** (Free): Good for testing and light usage
- **CPU Upgrade** ($0.05/hour): Better performance for regular use
- **GPU T4** ($0.60/hour): Fastest processing for heavy usage

### Environment Variables

You can add environment variables in the Space settings:

```bash
# Optional: Set custom timeouts
EXTRACTION_TIMEOUT=600

# Optional: Set custom temp directory
TEMP_DIR=/tmp/slides
```

## 📁 File Structure

Your Hugging Face Space should have this structure:

```
youtube-slide-extractor/
├── app.py                 # Main Gradio application
├── requirements.txt       # Python dependencies
├── packages.txt          # System packages
├── README.md             # Space documentation
├── .gitignore           # Git ignore file
└── slides/              # Output directory (created automatically)
```

## 🐛 Troubleshooting

### Common Issues

**1. Build Fails with Package Errors**
```bash
# Check packages.txt for typos
# Ensure all package names are correct for Ubuntu
```

**2. Import Errors**
```bash
# Check requirements.txt
# Ensure all Python packages are available on PyPI
# Use specific version numbers if needed
```

**3. OCR Not Working**
```bash
# Ensure tesseract-ocr is in packages.txt
# Check that pytesseract is in requirements.txt
```

**4. Video Download Fails**
```bash
# yt-dlp might need updates
# Some videos may be restricted
# Check YouTube URL format
```

### Performance Issues

**Slow Processing:**
- Upgrade to CPU Upgrade or GPU hardware
- Reduce video quality in download settings
- Process shorter videos

**Memory Issues:**
- Upgrade hardware tier
- Implement better cleanup in the code
- Process videos in smaller chunks

## 🔒 Security Considerations

### Safe Practices

1. **No API Keys**: Don't commit API keys to the repository
2. **Input Validation**: The app validates YouTube URLs
3. **Resource Limits**: Processing is limited by hardware tier
4. **Temporary Files**: Files are cleaned up automatically

### Content Policy

- Respect YouTube's Terms of Service
- Only process videos you have permission to use
- Educational use is generally acceptable
- Commercial use may require additional permissions

## 📊 Monitoring and Analytics

### Usage Tracking

Hugging Face provides built-in analytics:
- View count and user engagement
- Performance metrics
- Error logs and debugging info

### Custom Logging

Add custom logging to track usage:

```python
import logging

# Add to your app.py
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Log usage
logger.info(f"Processing video: {video_url}")
```

## 🚀 Advanced Features

### Custom Domains

You can set up a custom domain for your Space:
1. Go to Space settings
2. Add your custom domain
3. Configure DNS settings

### API Access

Enable API access for programmatic use:
1. Go to Space settings
2. Enable "API" option
3. Use the provided API endpoint

## 📈 Scaling Considerations

### High Traffic

For high-traffic scenarios:
- Upgrade to GPU hardware
- Implement request queuing
- Consider rate limiting
- Monitor resource usage

### Enterprise Use

For enterprise deployment:
- Consider Hugging Face Enterprise
- Implement user authentication
- Add usage analytics
- Set up monitoring and alerts

## 🤝 Community Features

### Sharing and Collaboration

- Make your Space public for community use
- Accept contributions via pull requests
- Add discussions for user feedback
- Create documentation and tutorials

### Integration

- Embed in websites using iframe
- Use API for integration with other tools
- Create widgets for easy sharing
- Connect with other Hugging Face models

---

## 📞 Support

If you encounter issues:

1. **Check Logs**: Review build and runtime logs
2. **Community**: Ask in Hugging Face Discord/Forums
3. **Documentation**: Review Hugging Face Spaces docs
4. **GitHub Issues**: Report bugs in the project repository

Good luck with your deployment! 🎉
