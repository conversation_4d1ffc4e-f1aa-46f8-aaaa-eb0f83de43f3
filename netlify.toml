[build]
  base = "frontend"
  publish = "frontend/build"
  command = "npm run build"

[build.environment]
  REACT_APP_API_URL = "https://slide-extractor-api.onrender.com"
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production.environment]
  REACT_APP_API_URL = "https://slide-extractor-api.onrender.com"

[context.deploy-preview.environment]
  REACT_APP_API_URL = "https://slide-extractor-api.onrender.com"

[context.branch-deploy.environment]
  REACT_APP_API_URL = "https://slide-extractor-api.onrender.com"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
