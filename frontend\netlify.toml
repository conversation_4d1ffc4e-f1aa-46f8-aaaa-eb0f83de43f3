[build]
  publish = "build"
  command = "npm run build"

[build.environment]
  REACT_APP_API_URL = "https://last-api-4ybg.onrender.com"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production.environment]
  REACT_APP_API_URL = "https://last-api-4ybg.onrender.com"

[context.deploy-preview.environment]
  REACT_APP_API_URL = "https://last-api-4ybg.onrender.com"

[context.branch-deploy.environment]
  REACT_APP_API_URL = "https://last-api-4ybg.onrender.com"
