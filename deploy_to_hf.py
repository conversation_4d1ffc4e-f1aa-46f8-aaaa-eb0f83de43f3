#!/usr/bin/env python3
"""
Deployment script for Hugging Face Spaces
Automates the process of preparing files for HF deployment
"""

import os
import shutil
import sys
from pathlib import Path

def create_deployment_package():
    """Create a deployment package for Hugging Face Spaces."""
    
    print("🚀 Creating Hugging Face Spaces deployment package...")
    
    # Create deployment directory
    deploy_dir = Path("hf_deployment")
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    deploy_dir.mkdir()
    
    print(f"📁 Created deployment directory: {deploy_dir}")
    
    # File mappings (source -> destination)
    file_mappings = {
        "hf_app.py": "app.py",
        "requirements_hf.txt": "requirements.txt",
        "packages.txt": "packages.txt",
        "README_HF.md": "README.md"
    }
    
    # Copy files
    for source, dest in file_mappings.items():
        source_path = Path(source)
        dest_path = deploy_dir / dest
        
        if source_path.exists():
            shutil.copy2(source_path, dest_path)
            print(f"✅ Copied {source} → {dest}")
        else:
            print(f"❌ Warning: {source} not found")
    
    # Create .gitignore
    gitignore_content = """
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/

# Temporary files
*.tmp
*.temp
slides/
temp/

# Logs
*.log

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Gradio
gradio_cached_examples/
flagged/
"""
    
    with open(deploy_dir / ".gitignore", "w", encoding="utf-8") as f:
        f.write(gitignore_content.strip())
    print("✅ Created .gitignore")
    
    # Create deployment instructions
    instructions = f"""
# 🚀 Hugging Face Deployment Package

This directory contains all files needed to deploy to Hugging Face Spaces.

## Quick Deployment Steps:

1. Create a new Space on Hugging Face:
   - Go to https://huggingface.co/spaces
   - Click "Create new Space"
   - Choose Gradio SDK
   - Set app_file to "app.py"

2. Clone your space repository:
   ```bash
   git clone https://huggingface.co/spaces/YOUR_USERNAME/YOUR_SPACE_NAME
   cd YOUR_SPACE_NAME
   ```

3. Copy all files from this directory to your space:
   ```bash
   cp {deploy_dir.absolute()}/* ./
   ```

4. Commit and push:
   ```bash
   git add .
   git commit -m "Initial deployment"
   git push origin main
   ```

## Files Included:

- **app.py**: Main Gradio application
- **requirements.txt**: Python dependencies
- **packages.txt**: System packages for Ubuntu
- **README.md**: Space documentation with metadata
- **.gitignore**: Git ignore rules

## Hardware Recommendations:

- **CPU Basic** (Free): For testing and light usage
- **CPU Upgrade** ($0.05/hour): For regular usage
- **GPU T4** ($0.60/hour): For heavy usage and faster processing

## Support:

- Check the build logs if deployment fails
- Ensure all dependencies are available
- Test locally with `gradio app.py` before deploying

Good luck! 🎉
"""
    
    with open(deploy_dir / "DEPLOYMENT_INSTRUCTIONS.md", "w", encoding="utf-8") as f:
        f.write(instructions.strip())
    print("✅ Created deployment instructions")
    
    print(f"\n🎉 Deployment package ready in: {deploy_dir.absolute()}")
    print("\n📋 Next steps:")
    print("1. Create a new Hugging Face Space")
    print("2. Clone the space repository")
    print("3. Copy files from the deployment directory")
    print("4. Commit and push to deploy")
    print(f"\nSee {deploy_dir}/DEPLOYMENT_INSTRUCTIONS.md for detailed steps.")

def validate_files():
    """Validate that all required files exist."""
    required_files = [
        "hf_app.py",
        "requirements_hf.txt", 
        "packages.txt",
        "README_HF.md"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure all files are present before deployment.")
        return False
    
    print("✅ All required files found")
    return True

def main():
    """Main deployment function."""
    print("🎓 YouTube Slide Extractor - Hugging Face Deployment")
    print("=" * 50)
    
    # Validate files
    if not validate_files():
        sys.exit(1)
    
    # Create deployment package
    create_deployment_package()
    
    print("\n" + "=" * 50)
    print("✨ Deployment package created successfully!")

if __name__ == "__main__":
    main()
