Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: gradio in c:\users\<USER>\appdata\roaming\python\python311\site-packages (5.25.0)
Requirement already satisfied: aiofiles<25.0,>=22.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (23.2.1)
Requirement already satisfied: anyio<5.0,>=3.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (4.9.0)
Requirement already satisfied: fastapi<1.0,>=0.115.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.115.12)
Requirement already satisfied: ffmpy in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.5.0)
Requirement already satisfied: gradio-client==1.8.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (1.8.0)
Requirement already satisfied: groovy~=0.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.1.2)
Requirement already satisfied: httpx>=0.24.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.28.1)
Requirement already satisfied: huggingface-hub>=0.28.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.30.2)
Requirement already satisfied: jinja2<4.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (3.1.6)
Requirement already satisfied: markupsafe<4.0,>=2.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (2.1.5)
Requirement already satisfied: numpy<3.0,>=1.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (1.26.4)
Requirement already satisfied: orjson~=3.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (3.10.12)
Requirement already satisfied: packaging in c:\programdata\miniconda3\lib\site-packages (from gradio) (23.2)
Requirement already satisfied: pandas<3.0,>=1.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (2.1.3)
Requirement already satisfied: pillow<12.0,>=8.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (10.4.0)
Requirement already satisfied: pydantic<2.12,>=2.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (2.10.1)
Requirement already satisfied: pydub in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.25.1)
Requirement already satisfied: python-multipart>=0.0.18 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.0.20)
Requirement already satisfied: pyyaml<7.0,>=5.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (6.0.1)
Requirement already satisfied: ruff>=0.9.3 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.11.5)
Requirement already satisfied: safehttpx<0.2.0,>=0.1.6 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.1.6)
Requirement already satisfied: semantic-version~=2.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (2.10.0)
Requirement already satisfied: starlette<1.0,>=0.40.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.46.1)
Requirement already satisfied: tomlkit<0.14.0,>=0.12.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.12.0)
Requirement already satisfied: typer<1.0,>=0.12 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.16.0)
Requirement already satisfied: typing-extensions~=4.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (4.12.2)
Requirement already satisfied: uvicorn>=0.14.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio) (0.27.1)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio-client==1.8.0->gradio) (2024.12.0)
Requirement already satisfied: websockets<16.0,>=10.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from gradio-client==1.8.0->gradio) (15.0.1)
Requirement already satisfied: idna>=2.8 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from anyio<5.0,>=3.0->gradio) (2.10)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from anyio<5.0,>=3.0->gradio) (1.3.1)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from httpx>=0.24.1->gradio) (2024.12.14)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from httpx>=0.24.1->gradio) (1.0.7)
Requirement already satisfied: h11<0.15,>=0.13 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from httpcore==1.*->httpx>=0.24.1->gradio) (0.14.0)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from huggingface-hub>=0.28.1->gradio) (3.16.1)
Requirement already satisfied: requests in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from huggingface-hub>=0.28.1->gradio) (2.32.3)
Requirement already satisfied: tqdm>=4.42.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from huggingface-hub>=0.28.1->gradio) (4.66.1)
Requirement already satisfied: python-dateutil>=2.8.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pandas<3.0,>=1.0->gradio) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pandas<3.0,>=1.0->gradio) (2024.2)
Requirement already satisfied: tzdata>=2022.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pandas<3.0,>=1.0->gradio) (2024.2)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pydantic<2.12,>=2.0->gradio) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pydantic<2.12,>=2.0->gradio) (2.27.1)
Requirement already satisfied: click>=8.0.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from typer<1.0,>=0.12->gradio) (8.1.8)
Requirement already satisfied: shellingham>=1.3.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from typer<1.0,>=0.12->gradio) (1.5.4)
Requirement already satisfied: rich>=10.11.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from typer<1.0,>=0.12->gradio) (13.9.4)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from click>=8.0.0->typer<1.0,>=0.12->gradio) (0.4.6)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio) (1.17.0)
Requirement already satisfied: markdown-it-py>=2.2.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (2.18.0)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\programdata\miniconda3\lib\site-packages (from requests->huggingface-hub>=0.28.1->gradio) (2.0.4)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from requests->huggingface-hub>=0.28.1->gradio) (2.3.0)
Requirement already satisfied: mdurl~=0.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio) (0.1.2)
