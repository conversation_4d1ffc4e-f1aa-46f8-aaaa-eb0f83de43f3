#!/usr/bin/env python3
"""
Hugging Face Gradio Slide Extractor Application
Streamlined version for Hugging Face Spaces deployment
"""

import gradio as gr
import os
import sys
import json
import time
import tempfile
import threading
import logging
import uuid
import shutil
import subprocess
from datetime import datetime
from typing import Optional, Tuple, Dict, Any, List
from pathlib import Path
import zipfile

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("HF_SlideExtractor")

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import components with fallbacks
try:
    import cv2
    import numpy as np
    from PIL import Image, ImageEnhance
    import pytesseract
    from skimage.metrics import structural_similarity as ssim
    CV_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Computer vision libraries not available: {e}")
    CV_AVAILABLE = False

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.utils import ImageReader
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

# Global variables
extraction_jobs = {}
job_lock = threading.Lock()

class SimpleSlideExtractor:
    """Simplified slide extractor for Hugging Face deployment."""
    
    def __init__(self, video_url, output_dir, callback=None):
        self.video_url = video_url
        self.output_dir = output_dir
        self.callback = callback
        self.slides = []
        self.video_path = None
        
    def download_video(self):
        """Download video using yt-dlp."""
        try:
            if self.callback:
                self.callback("Downloading video...")
            
            # Create temp directory for video
            temp_dir = tempfile.mkdtemp()
            
            # Use yt-dlp to download video
            cmd = [
                "yt-dlp",
                "--format", "best[height<=720]",  # Limit quality for faster processing
                "--output", os.path.join(temp_dir, "video.%(ext)s"),
                "--no-playlist",
                self.video_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                # Find the downloaded video file
                for file in os.listdir(temp_dir):
                    if file.startswith("video."):
                        self.video_path = os.path.join(temp_dir, file)
                        return True
            else:
                logger.error(f"yt-dlp error: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Download error: {e}")
            return False
    
    def extract_frames(self):
        """Extract frames from video."""
        if not CV_AVAILABLE or not self.video_path:
            return False
            
        try:
            if self.callback:
                self.callback("Extracting frames...")
                
            cap = cv2.VideoCapture(self.video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps
            
            # Extract one frame every 5 seconds
            interval = 5
            frames_to_extract = int(duration / interval)
            
            extracted_frames = []
            for i in range(frames_to_extract):
                frame_time = i * interval
                frame_number = int(frame_time * fps)
                
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                ret, frame = cap.read()
                
                if ret:
                    # Save frame
                    frame_path = os.path.join(self.output_dir, f"frame_{i:04d}.jpg")
                    cv2.imwrite(frame_path, frame)
                    extracted_frames.append({
                        'path': frame_path,
                        'timestamp': frame_time,
                        'frame_number': i
                    })
                    
                if self.callback:
                    progress = (i + 1) / frames_to_extract * 50  # 50% of total progress
                    self.callback(f"Extracting frames... {progress:.1f}%")
            
            cap.release()
            return extracted_frames
            
        except Exception as e:
            logger.error(f"Frame extraction error: {e}")
            return []
    
    def filter_slides(self, frames):
        """Filter frames to find unique slides."""
        if not frames:
            return []
            
        try:
            if self.callback:
                self.callback("Filtering unique slides...")
                
            slides = []
            prev_frame = None
            
            for i, frame_info in enumerate(frames):
                frame_path = frame_info['path']
                
                if not os.path.exists(frame_path):
                    continue
                    
                # Load current frame
                current_frame = cv2.imread(frame_path, cv2.IMREAD_GRAYSCALE)
                
                if current_frame is None:
                    continue
                
                # Resize for faster comparison
                current_frame = cv2.resize(current_frame, (320, 240))
                
                # Compare with previous frame
                is_different = True
                if prev_frame is not None:
                    # Calculate similarity
                    similarity = ssim(prev_frame, current_frame)
                    is_different = similarity < 0.95  # 95% similarity threshold
                
                if is_different:
                    # Extract text using OCR
                    text_content = self.extract_text(frame_path)
                    
                    slide_info = {
                        'filename': os.path.basename(frame_path),
                        'path': frame_path,
                        'timestamp': frame_info['timestamp'],
                        'content': text_content,
                        'slide_number': len(slides) + 1
                    }
                    slides.append(slide_info)
                    prev_frame = current_frame.copy()
                
                if self.callback:
                    progress = 50 + (i + 1) / len(frames) * 40  # 40% of total progress
                    self.callback(f"Processing frames... {progress:.1f}%")
            
            return slides
            
        except Exception as e:
            logger.error(f"Slide filtering error: {e}")
            return []
    
    def extract_text(self, image_path):
        """Extract text from image using OCR."""
        try:
            # Load and preprocess image
            image = cv2.imread(image_path)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply preprocessing
            gray = cv2.medianBlur(gray, 3)
            
            # Extract text
            text = pytesseract.image_to_string(gray, config='--psm 6')
            return text.strip()
            
        except Exception as e:
            logger.error(f"OCR error: {e}")
            return ""
    
    def create_pdf(self):
        """Create PDF from slides."""
        if not PDF_AVAILABLE or not self.slides:
            return None
            
        try:
            if self.callback:
                self.callback("Creating PDF...")
                
            pdf_path = os.path.join(self.output_dir, "slides.pdf")
            c = canvas.Canvas(pdf_path, pagesize=A4)
            
            for i, slide in enumerate(self.slides):
                if os.path.exists(slide['path']):
                    # Add image to PDF
                    img = ImageReader(slide['path'])
                    c.drawImage(img, 50, 400, width=500, height=300, preserveAspectRatio=True)
                    
                    # Add text content
                    if slide.get('content'):
                        text_lines = slide['content'][:500].split('\n')[:10]  # Limit text
                        y_pos = 350
                        for line in text_lines:
                            if line.strip():
                                c.drawString(50, y_pos, line[:80])  # Limit line length
                                y_pos -= 15
                    
                    c.showPage()
                
                if self.callback:
                    progress = 90 + (i + 1) / len(self.slides) * 10
                    self.callback(f"Creating PDF... {progress:.1f}%")
            
            c.save()
            return pdf_path
            
        except Exception as e:
            logger.error(f"PDF creation error: {e}")
            return None
    
    def extract_slides(self):
        """Main extraction method."""
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            
            # Download video
            if not self.download_video():
                return False
            
            # Extract frames
            frames = self.extract_frames()
            if not frames:
                return False
            
            # Filter unique slides
            self.slides = self.filter_slides(frames)
            
            if self.callback:
                self.callback(f"Extraction complete! Found {len(self.slides)} slides.")
            
            return len(self.slides) > 0
            
        except Exception as e:
            logger.error(f"Extraction error: {e}")
            return False
    
    def get_slides(self):
        """Get extracted slides."""
        return self.slides

def extract_slides_job(job_id, video_url, options):
    """Background job for slide extraction."""
    try:
        with job_lock:
            if job_id not in extraction_jobs:
                return
            extraction_jobs[job_id]['status'] = 'processing'
            extraction_jobs[job_id]['progress'] = 0
        
        # Create output directory
        output_dir = os.path.join("slides", job_id)
        os.makedirs(output_dir, exist_ok=True)
        
        def update_progress(message):
            with job_lock:
                if job_id in extraction_jobs:
                    extraction_jobs[job_id]['message'] = message
                    logger.info(f"Job {job_id}: {message}")
        
        # Create extractor
        extractor = SimpleSlideExtractor(
            video_url=video_url,
            output_dir=output_dir,
            callback=update_progress
        )
        
        # Extract slides
        success = extractor.extract_slides()
        
        if success:
            slides = extractor.get_slides()
            
            # Create PDF if requested
            pdf_path = None
            if options.get('generate_pdf', True):
                pdf_path = extractor.create_pdf()
            
            # Create results zip
            zip_path = os.path.join(output_dir, "results.zip")
            with zipfile.ZipFile(zip_path, 'w') as zipf:
                for slide in slides:
                    if os.path.exists(slide['path']):
                        zipf.write(slide['path'], os.path.basename(slide['path']))
                
                if pdf_path and os.path.exists(pdf_path):
                    zipf.write(pdf_path, "slides.pdf")
                
                # Add metadata
                metadata = {
                    'video_url': video_url,
                    'extraction_time': datetime.now().isoformat(),
                    'slides_count': len(slides),
                    'slides': slides
                }
                zipf.writestr("metadata.json", json.dumps(metadata, indent=2))
            
            with job_lock:
                extraction_jobs[job_id].update({
                    'status': 'completed',
                    'progress': 100,
                    'message': f'Completed! Found {len(slides)} slides.',
                    'slides': slides,
                    'zip_path': zip_path,
                    'pdf_path': pdf_path
                })
        else:
            with job_lock:
                extraction_jobs[job_id].update({
                    'status': 'failed',
                    'message': 'Extraction failed. Please check the video URL and try again.'
                })
    
    except Exception as e:
        logger.error(f"Job {job_id} error: {e}")
        with job_lock:
            extraction_jobs[job_id].update({
                'status': 'failed',
                'message': f'Error: {str(e)}'
            })

def start_extraction(video_url, generate_pdf=True):
    """Start slide extraction process."""
    if not video_url.strip():
        return "❌ Please enter a valid video URL", "", None, None

    # Generate job ID
    job_id = str(uuid.uuid4())[:8]

    # Create job
    with job_lock:
        extraction_jobs[job_id] = {
            'id': job_id,
            'status': 'starting',
            'progress': 0,
            'message': 'Starting extraction...',
            'video_url': video_url,
            'created_at': datetime.now().isoformat()
        }

    # Start background job
    options = {'generate_pdf': generate_pdf}
    thread = threading.Thread(
        target=extract_slides_job,
        args=(job_id, video_url, options),
        daemon=True
    )
    thread.start()

    return f"✅ Extraction started! Job ID: {job_id}", job_id, None, None

def check_job_status(job_id):
    """Check job status and return results."""
    if not job_id:
        return "No job ID provided", None, None

    with job_lock:
        job = extraction_jobs.get(job_id)

    if not job:
        return "Job not found", None, None

    status_msg = f"Status: {job['status']} - {job.get('message', '')}"

    if job['status'] == 'completed':
        zip_path = job.get('zip_path')
        pdf_path = job.get('pdf_path')

        return status_msg, zip_path, pdf_path

    return status_msg, None, None

# Create Gradio interface
def create_interface():
    """Create the Gradio interface."""

    with gr.Blocks(title="YouTube Slide Extractor", theme=gr.themes.Soft()) as app:
        gr.Markdown("""
        # 🎓 YouTube Slide Extractor

        Extract slides from educational YouTube videos automatically using computer vision and OCR.

        **Features:**
        - Automatic slide detection and extraction
        - OCR text extraction from slides
        - PDF generation
        - Downloadable results

        **Instructions:**
        1. Paste a YouTube video URL
        2. Click "Start Extraction"
        3. Wait for processing to complete
        4. Download your results!
        """)

        with gr.Row():
            with gr.Column(scale=2):
                video_url = gr.Textbox(
                    label="YouTube Video URL",
                    placeholder="https://www.youtube.com/watch?v=...",
                    lines=1
                )

                generate_pdf = gr.Checkbox(
                    label="Generate PDF",
                    value=True
                )

                extract_btn = gr.Button("🚀 Start Extraction", variant="primary")

            with gr.Column(scale=1):
                status_output = gr.Textbox(
                    label="Status",
                    lines=3,
                    interactive=False
                )

                job_id_output = gr.Textbox(
                    label="Job ID",
                    interactive=False,
                    visible=False
                )

        with gr.Row():
            check_btn = gr.Button("🔄 Check Status")

        with gr.Row():
            with gr.Column():
                zip_download = gr.File(
                    label="📁 Download All Slides (ZIP)",
                    visible=False
                )

            with gr.Column():
                pdf_download = gr.File(
                    label="📄 Download PDF",
                    visible=False
                )

        # Event handlers
        extract_btn.click(
            fn=start_extraction,
            inputs=[video_url, generate_pdf],
            outputs=[status_output, job_id_output, zip_download, pdf_download]
        ).then(
            lambda: gr.update(visible=True),
            outputs=[job_id_output]
        )

        check_btn.click(
            fn=check_job_status,
            inputs=[job_id_output],
            outputs=[status_output, zip_download, pdf_download]
        ).then(
            lambda zip_path, pdf_path: (
                gr.update(visible=bool(zip_path)),
                gr.update(visible=bool(pdf_path))
            ),
            inputs=[zip_download, pdf_download],
            outputs=[zip_download, pdf_download]
        )

        # Auto-refresh status every 5 seconds
        app.load(
            fn=lambda: None,
            every=5
        )

    return app

if __name__ == "__main__":
    # Create and launch the app
    app = create_interface()
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False
    )
