# Production Requirements for Scalable Deployment
# Includes API server, database, and task queue capabilities

# Include minimal requirements
-r requirements_minimal.txt

# Web Framework
flask>=2.0.0
flask-cors>=4.0.0
flask-sqlalchemy>=3.0.0
flask-migrate>=4.0.0
werkzeug>=2.0.0
gunicorn>=21.0.0

# Database and Caching
sqlalchemy>=2.0.0
alembic>=1.12.0
redis>=4.5.0
psycopg2-binary>=2.9.0

# Task Queue and Scalability
celery>=5.3.0
celery[redis]>=5.3.0

# Configuration
python-dotenv>=1.0.0

# Additional Processing
moviepy>=1.0.0
reportlab>=3.6.0
matplotlib>=3.4.0
scikit-image>=0.18.0
nltk>=3.6.0

# Development and Debugging
setuptools>=65.0.0

# Note: This configuration is optimized for production deployment
# with horizontal scaling capabilities
