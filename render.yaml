services:
  # Main API Service (Auto-configuring)
  - type: web
    name: slide-extractor-api
    env: python
    buildCommand: |
      pip install --upgrade pip &&
      pip install -r requirements.txt &&
      python fix_deployment.py
    startCommand: python start_app.py
    plan: standard
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: PORT
        value: 10000
      - key: ENVIRONMENT
        value: production
      - key: USE_CELERY
        value: "false"  # Auto-detected by start_app.py
      - key: USE_REDIS
        value: "false"  # Auto-detected by start_app.py
      - key: CORS_ALLOWED_ORIGINS
        value: "https://latenighter.netlify.app"
      - key: GEMINI_API_KEY
        sync: false  # Set manually in dashboard
      - key: MAX_CONTENT_LENGTH
        value: "104857600"  # 100MB
      - key: UPLOAD_TIMEOUT
        value: "600"  # 10 minutes
      - key: FLASK_APP
        value: app.py
    healthCheckPath: /api/status
