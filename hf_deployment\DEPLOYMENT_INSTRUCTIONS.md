# 🚀 Hugging Face Deployment Package

This directory contains all files needed to deploy to Hugging Face Spaces.

## Quick Deployment Steps:

1. Create a new Space on Hugging Face:
   - Go to https://huggingface.co/spaces
   - Click "Create new Space"
   - Choose Gradio SDK
   - Set app_file to "app.py"

2. Clone your space repository:
   ```bash
   git clone https://huggingface.co/spaces/YOUR_USERNAME/YOUR_SPACE_NAME
   cd YOUR_SPACE_NAME
   ```

3. Copy all files from this directory to your space:
   ```bash
   cp C:\Users\<USER>\Downloads\day1\backend_deploy\hf_deployment/* ./
   ```

4. Commit and push:
   ```bash
   git add .
   git commit -m "Initial deployment"
   git push origin main
   ```

## Files Included:

- **app.py**: Main Gradio application
- **requirements.txt**: Python dependencies
- **packages.txt**: System packages for Ubuntu
- **README.md**: Space documentation with metadata
- **.gitignore**: Git ignore rules

## Hardware Recommendations:

- **CPU Basic** (Free): For testing and light usage
- **CPU Upgrade** ($0.05/hour): For regular usage
- **GPU T4** ($0.60/hour): For heavy usage and faster processing

## Support:

- Check the build logs if deployment fails
- Ensure all dependencies are available
- Test locally with `gradio app.py` before deploying

Good luck! 🎉