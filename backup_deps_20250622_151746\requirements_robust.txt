# Robust YouTube Downloader Requirements
# Enhanced dependencies for maximum download success rates

# Core Gradio Application
gradio>=3.50.0,<4.0.0

# Computer Vision and Image Processing
opencv-python>=4.5.0
numpy>=1.19.0
Pillow>=8.0.0
scikit-image>=0.18.0

# OCR and Text Processing
pytesseract>=0.3.8
nltk>=3.6.0

# Video Processing and Download (Multiple Methods)
yt-dlp>=2023.1.6
pytube>=15.0.0
youtube-dl>=2021.12.17
youtube-transcript-api>=0.6.0

# Enhanced Video Processing
moviepy>=1.0.0
ffmpeg-python>=0.2.0

# PDF Generation
reportlab>=3.6.0

# Plotting and Visualization
matplotlib>=3.4.0

# HTTP Requests and Web Scraping
requests>=2.31.0
httpx>=0.24.0
aiohttp>=3.9.0

# AI and Machine Learning
google-generativeai>=0.3.0

# System Utilities
psutil>=5.9.0

# Enhanced Browser Simulation
selenium>=4.15.0
webdriver-manager>=4.0.0
fake-useragent>=1.4.0

# Proxy Support (Optional)
requests[socks]>=2.31.0
pysocks>=1.7.1

# Async Support
asyncio>=3.4.3
aiofiles>=23.0.0

# Data Processing
pandas>=1.3.0
json5>=0.9.0

# Logging and Monitoring
structlog>=23.0.0
rich>=13.0.0

# Development and Debugging
setuptools>=65.0.0

# Network and Security
certifi>=2023.0.0
urllib3>=1.26.0
cryptography>=41.0.0

# Additional YouTube Download Libraries (Fallbacks)
pafy>=0.5.5
youtube-search-python>=1.6.6

# Text Processing and NLP
textblob>=0.17.1
spacy>=3.4.0

# Image Processing Enhancements
opencv-contrib-python>=4.5.0
imageio>=2.22.0

# Audio Processing
librosa>=0.9.0
soundfile>=0.12.0

# Database Support
sqlalchemy>=1.4.0
sqlite3  # Built-in with Python

# Configuration Management
python-dotenv>=1.0.0
pyyaml>=6.0.0

# Error Handling and Retry Logic
tenacity>=8.2.0
backoff>=2.2.0

# Performance Monitoring
memory-profiler>=0.61.0
line-profiler>=4.0.0

# Note: Some dependencies are optional and the application will work
# with reduced functionality if they are not installed.
# 
# For basic functionality, install:
# pip install gradio opencv-python numpy Pillow requests yt-dlp
#
# For robust functionality, install:
# pip install -r requirements_robust.txt
#
# For cloud deployment (Render.com), consider:
# - Setting up proxy services for better success rates
# - Using environment variables for API keys
# - Enabling request throttling and retry logic
